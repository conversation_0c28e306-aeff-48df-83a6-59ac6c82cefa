import { AIMessage } from "@langchain/core/messages";
import { parseModelOutput, extractHtmlFromToolResult } from "./utils.js";

// 模拟测试场景
function testOutputExtraction() {
  console.log("=== 测试输出提取逻辑 ===\n");

  // 场景1: 从工具调用结果中提取
  console.log("场景1: 从工具调用结果中提取");
  const toolMessage = new AIMessage("", {
    tool_calls: [
      {
        id: "1",
        name: "writeFile",
        args: JSON.stringify({
          fileName: "index.html",
          content: "<html><body><h1>最终生成的代码</h1></body></html>",
        }),
      },
    ],
  });

  const htmlFromTool = extractHtmlFromToolResult(toolMessage);
  console.log("工具调用结果:", htmlFromTool);
  console.log("");

  // 场景2: 从消息内容中提取
  console.log("场景2: 从消息内容中提取");
  const contentMessage = new AIMessage(
    "<html><body><h1>直接返回的代码</h1></body></html>"
  );
  const htmlFromContent = parseModelOutput(contentMessage);
  console.log("消息内容结果:", htmlFromContent);
  console.log("");

  // 场景3: 模拟finalCheck逻辑
  console.log("场景3: 模拟finalCheck逻辑");
  const messages = [
    new AIMessage("第一步"),
    new AIMessage("第二步"),
    toolMessage, // 最后一条消息包含工具调用
  ];

  let finalHtml = "";
  const lastMessage = messages[messages.length - 1];

  if (lastMessage && lastMessage instanceof AIMessage) {
    // 首先尝试从工具调用结果中提取
    const htmlFromTool = extractHtmlFromToolResult(lastMessage);
    if (htmlFromTool) {
      finalHtml = htmlFromTool;
      console.log("从工具调用结果中提取:", finalHtml);
    } else {
      // 如果没有工具调用，尝试从消息内容中提取
      const htmlFromContent = parseModelOutput(lastMessage);
      if (htmlFromContent) {
        finalHtml = htmlFromContent;
        console.log("从消息内容中提取:", finalHtml);
      }
    }
  }

  console.log("最终HTML代码:", finalHtml);
  console.log("最终HTML代码长度:", finalHtml.length);
}

// 运行测试
testOutputExtraction();
