# selectedAgent 判断逻辑修复

## 问题描述

在 `web/src/app/chat/[thread_id]/page.tsx` 中，`selectedAgent` 的判断逻辑有问题：

- 原来的逻辑：`const selectedAgent = searchParams.get('agent') || DEFAULT_AGENT;`
- 问题：只考虑了 URL 参数，没有考虑历史 thread 的 metadata.graph_id 字段

## 修复方案

修改 `selectedAgent` 的判断逻辑，采用优先级策略：

1. **优先级1**：从 URL 参数读取（`searchParams.get('agent')`）- 适用于从首页跳转过来的情况
2. **优先级2**：从 thread 的 metadata.graph_id 读取 - 适用于读取历史 thread 的情况  
3. **优先级3**：使用默认值（`DEFAULT_AGENT`）- 兜底方案

## 修改内容

### 文件：`web/src/app/chat/[thread_id]/page.tsx`

**修改前：**
```typescript
const selectedAgent = searchParams.get('agent') || DEFAULT_AGENT;
```

**修改后：**
```typescript
// 动态计算 selectedAgent
const selectedAgent = useMemo(() => {
  // 1. 优先从 URL 参数读取（从首页跳转过来）
  const urlAgent = searchParams.get('agent');
  if (urlAgent) {
    return urlAgent;
  }
  
  // 2. 从 thread 的 metadata.graph_id 读取（历史 thread）
  if (initialFetchedThread?.metadata?.graph_id) {
    return initialFetchedThread.metadata.graph_id as string;
  }
  
  // 3. 使用默认值
  return DEFAULT_AGENT;
}, [searchParams, initialFetchedThread]);
```

## 技术细节

1. **使用 useMemo**：避免不必要的重新计算，只有当依赖项变化时才重新计算
2. **依赖项**：`[searchParams, initialFetchedThread]` - 当 URL 参数或线程数据变化时重新计算
3. **类型安全**：使用 `as string` 确保类型转换正确
4. **导入更新**：添加了 `useMemo` 的导入

## 测试场景

修复后应该能够正确处理以下场景：

1. **从首页跳转**：URL 包含 `?agent=xxx` 参数
2. **历史线程访问**：直接访问 `/chat/[thread_id]` 路径，从线程元数据读取 graph_id
3. **兜底情况**：既没有 URL 参数也没有线程元数据时使用默认值

## 完成时间

2024年12月19日

## 状态

✅ 已完成
