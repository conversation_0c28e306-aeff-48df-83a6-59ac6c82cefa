"use client";

import { useState } from "react";
import { Message } from "@langchain/langgraph-sdk";
import { AnimatePresence, motion } from "framer-motion";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, User, AlertCircle } from "lucide-react";
import { Textarea } from "./ui/textarea";
import { Button } from "./ui/button";
import { Loader2 } from "lucide-react";

// 获取消息内容字符串的函数
function getMessageContentString(content: any): string {
  if (typeof content === 'string') {
    return content;
  }
  if (Array.isArray(content)) {
    return content.map(item => {
      if (typeof item === 'string') return item;
      if (item.text) return item.text;
      return JSON.stringify(item);
    }).join('');
  }
  if (content && typeof content === 'object' && content.text) {
    return content.text;
  }
  return JSON.stringify(content);
}

function MessageCopyButton({ content }: { content: string }) {
  const [copied, setCopied] = useState(false);

  const handleCopy = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e.stopPropagation();
    navigator.clipboard.writeText(content);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <Button
      onClick={(e) => handleCopy(e)}
      variant="ghost"
      size="sm"
      className="size-6 p-1 opacity-0 transition-opacity group-hover:opacity-100"
    >
      <AnimatePresence mode="wait" initial={false}>
        {copied ? (
          <motion.div
            key="check"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.15 }}
          >
            <CopyCheck className="h-3 w-3 text-green-500" />
          </motion.div>
        ) : (
          <motion.div
            key="copy"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.15 }}
          >
            <Copy className="h-3 w-3" />
          </motion.div>
        )}
      </AnimatePresence>
    </Button>
  );
}

interface ErrorState {
  message: string;
  details?: string;
}

interface ManagerChatProps {
  messages: any[];
  chatInput: string;
  setChatInput: (input: string) => void;
  handleSendMessage: () => void;
  isLoading: boolean;
  cancelRun: () => void;
  errorState?: ErrorState | null;
  githubUser?: {
    login: string;
    avatar_url: string;
    html_url: string;
    name: string | null;
    email: string | null;
  };
}

function extractResponseFromMessage(message: any): string {
  // 简化的消息内容提取逻辑
  return getMessageContentString(message.content);
}

function LoadingMessageDots() {
  return (
    <div className="text-foreground flex items-center space-x-1 overflow-x-hidden text-sm">
      <style jsx>{`
        @keyframes dotBounce {
          0%,
          80%,
          100% {
            transform: scale(0.8);
            opacity: 0.5;
          }
          40% {
            transform: scale(1.2);
            opacity: 1;
          }
        }
        .dot-bounce {
          animation: dotBounce 1.4s infinite ease-in-out;
        }
      `}</style>
      <div className="flex space-x-1">
        <div
          className="dot-bounce h-1 w-1 rounded-full bg-current"
          style={{ animationDelay: "0ms" }}
        />
        <div
          className="dot-bounce h-1 w-1 rounded-full bg-current"
          style={{ animationDelay: "200ms" }}
        />
        <div
          className="dot-bounce h-1 w-1 rounded-full bg-current"
          style={{ animationDelay: "400ms" }}
        />
      </div>
    </div>
  );
}

function CollapsibleAlert({ errorState }: { errorState: ErrorState }) {
  return (
    <div className="bg-destructive/10 border-destructive/20 rounded-lg border p-3">
      <div className="flex items-start gap-2">
        <AlertCircle className="text-destructive mt-0.5 h-4 w-4 flex-shrink-0" />
        <div className="min-w-0 flex-1">
          <p className="text-destructive text-sm font-medium">
            {errorState.message}
          </p>
          {errorState.details && (
            <p className="text-destructive/80 mt-1 text-xs">
              {errorState.details}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}

export function ManagerChat({
  messages,
  chatInput,
  setChatInput,
  handleSendMessage,
  isLoading,
  cancelRun,
  errorState,
  githubUser,
}: ManagerChatProps) {
  return (
    <div className="border-border bg-muted/30 flex h-full w-1/3 flex-col overflow-hidden border-r">
      <div className="relative flex-1">
        <div className="absolute inset-0 h-full overflow-y-auto">
          <div className="space-y-4 p-4">
            {messages.map((message) => {
              const messageContentString = extractResponseFromMessage(message);
              return (
                <div
                  key={message.id}
                  className="group bg-muted flex items-start gap-3 rounded-lg p-3"
                >
                  <div className="mt-0.5 flex-shrink-0">
                    {message.type === "human" ? (
                      githubUser?.avatar_url ? (
                        <div className="bg-muted flex h-6 w-6 items-center justify-center overflow-hidden rounded-full">
                          <img
                            src={githubUser.avatar_url}
                            alt={githubUser.login}
                            className="h-full w-full object-cover"
                          />
                        </div>
                      ) : (
                        <div className="bg-muted flex h-6 w-6 items-center justify-center rounded-full">
                          <User className="text-muted-foreground h-4 w-4" />
                        </div>
                      )
                    ) : (
                      <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-950/50">
                        <Bot className="h-4 w-4 text-blue-700 dark:text-blue-300" />
                      </div>
                    )}
                  </div>
                  <div className="relative min-w-0 flex-1 space-y-1 overflow-x-hidden">
                    <div className="flex items-center justify-between gap-2">
                      <span className="text-muted-foreground text-xs font-medium">
                        {message.type === "human"
                          ? githubUser?.login || "You"
                          : "Assistant"}
                      </span>
                      <MessageCopyButton content={messageContentString} />
                    </div>
                    {messageContentString ? (
                      <div className="text-foreground overflow-x-hidden text-sm whitespace-pre-wrap">
                        {messageContentString}
                      </div>
                    ) : (
                      <LoadingMessageDots />
                    )}
                  </div>
                </div>
              );
            })}
            {errorState && <CollapsibleAlert errorState={errorState} />}
          </div>
        </div>
      </div>

      <div className="border-border bg-muted/30 border-t p-4">
        <div className="flex gap-2">
          <Textarea
            value={chatInput}
            onChange={(e) => setChatInput(e.target.value)}
            placeholder="输入您的消息..."
            className="border-border bg-background text-foreground placeholder:text-muted-foreground min-h-[60px] flex-1 resize-none text-sm"
            onKeyDown={(e) => {
              if (e.key === "Enter" && (e.metaKey || e.ctrlKey) && !isLoading) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
          />
          {isLoading ? (
            <Button
              className="size-8 rounded-full"
              variant="destructive"
              onClick={cancelRun}
              size="icon"
            >
              <Loader2 className="size-4 animate-spin" />
            </Button>
          ) : (
            <Button
              onClick={handleSendMessage}
              disabled={!chatInput.trim()}
              size="icon"
              className="size-8 rounded-full"
            >
              <ArrowUp className="size-4" />
            </Button>
          )}
        </div>
        <div className="text-muted-foreground mt-2 text-xs">
          按 Cmd+Enter 发送
        </div>
      </div>
    </div>
  );
}
