"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, GitBranch } from "lucide-react";
import { useStream } from "@langchain/langgraph-sdk/react";
import { getAgentInfo } from "@/common/constants";
import { ManagerChat } from "./manager-chat";
import { ChatSidebarPanel } from "./chat/chat-sidebar-panel";
import { v4 as uuidv4 } from "uuid";
import { HumanMessage } from "@langchain/core/messages";

interface ThreadViewProps {
  stream: ReturnType<typeof useStream>;
  displayThread: any;
  onBackToHome: () => void;
  threadId: string;
  selectedAgent: string;
  initialThread?: any;
}

interface ErrorState {
  message: string;
  details?: string;
}

export function ThreadView({
  stream,
  displayThread,
  onBackToHome,
  threadId,
  selectedAgent,
  initialThread
}: ThreadViewProps) {
  const [chatInput, setChatInput] = useState("");
  const [errorState, setErrorState] = useState<ErrorState | null>(null);
  const [optimisticMessage, setOptimisticMessage] = useState<any | null>(null);

  // 处理stream错误
  useEffect(() => {
    if (stream.error) {
      const rawErrorMessage =
        typeof stream.error === "object" && "message" in stream.error
          ? (stream.error.message as string)
          : "发生未知错误";

      setErrorState({
        message: rawErrorMessage,
      });
    } else {
      setErrorState(null);
    }
  }, [stream.error]);

  // 从sessionStorage加载optimistic message
  useEffect(() => {
    try {
      const storedData = sessionStorage.getItem(
        `lg:initial-message:${threadId}`,
      );
      if (storedData) {
        const { message: stringifiedMessage } = JSON.parse(storedData);
        setOptimisticMessage(stringifiedMessage);
      }
    } catch (error) {
      console.error(
        "Failed to load optimistic message from sessionStorage:",
        error,
      );
    }
  }, [threadId, stream.messages.length]);

  // 如果有多条消息，移除optimistic message
  useEffect(() => {
    if (stream.messages.length > 1 && optimisticMessage) {
      setOptimisticMessage(null);
      if (threadId) {
        try {
          sessionStorage.removeItem(`lg:initial-message:${threadId}`);
        } catch (error) {
          console.error(
            "Failed to remove optimistic message from sessionStorage:",
            error,
          );
        }
      }
    }
  }, [stream.messages, optimisticMessage, threadId]);

  // 清理sessionStorage
  useEffect(() => {
    return () => {
      if (threadId) {
        try {
          sessionStorage.removeItem(`lg:initial-message:${threadId}`);
        } catch {
          // no-op
        }
      }
    };
  }, [threadId]);

  const getStatusDotColor = (status: string) => {
    switch (status) {
      case "running":
        return "bg-blue-500 dark:bg-blue-400";
      case "completed":
        return "bg-green-500 dark:bg-green-400";
      case "paused":
        return "bg-yellow-500 dark:bg-yellow-400";
      case "error":
        return "bg-red-500 dark:bg-red-400";
      default:
        return "bg-gray-500 dark:bg-gray-400";
    }
  };

  const cancelRun = () => {
    stream.stop();
  };

  const handleSendMessage = () => {
    if (chatInput.trim()) {
      const newHumanMessage = {
        id: uuidv4(),
        content: chatInput,
        type: "human" as const,
      };

      stream.submit(
        {
          input: chatInput.trim(),
          messages: [newHumanMessage],
        },
        {
          streamResumable: true,
        },
      );
      setChatInput("");
    }
  };

  // 过滤不需要渲染的消息
  const filteredMessages = stream.messages.filter((message: any) => {
    return !message.id?.startsWith("DO_NOT_RENDER");
  });

  // 合并optimistic message和stream messages
  const displayMessages = optimisticMessage
    ? [
        optimisticMessage,
        ...filteredMessages.filter((msg: any) => msg.id !== optimisticMessage.id),
      ]
    : filteredMessages;

  // 创建dummy thread数据
  const dummyThread = initialThread || {
    thread_id: threadId,
    values: {},
    status: "idle" as const,
    updated_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    metadata: {
      graph_id: selectedAgent,
      assistant_id: selectedAgent,
    },
    config: {},
  };

  return (
    <div className="bg-background flex h-screen flex-1 flex-col">
      {/* Header */}
      <div className="border-border bg-card absolute top-0 right-0 left-0 z-10 border-b px-4 py-2">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            className="text-muted-foreground hover:bg-muted hover:text-foreground h-6 w-6 p-0"
            onClick={onBackToHome}
          >
            <ArrowLeft className="h-3 w-3" />
          </Button>
          <div className="flex min-w-0 flex-1 items-center gap-2">
            <div
              className={`size-2 flex-shrink-0 rounded-full ${getStatusDotColor(
                stream.isLoading ? "running" : displayThread?.status || "completed"
              )}`}
            ></div>
            <span className="text-muted-foreground max-w-[500px] truncate font-mono text-sm">
              {displayThread?.title || getAgentInfo(selectedAgent).name}
            </span>
            {displayThread?.repository && (
              <>
                <span className="text-muted-foreground text-xs">•</span>
                <GitBranch className="text-muted-foreground h-3 w-3" />
                <span className="text-muted-foreground truncate text-xs">
                  {displayThread.repository}
                </span>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Main Content - Split Layout */}
      <div className="flex w-full pt-12">
        <ManagerChat
          messages={displayMessages}
          chatInput={chatInput}
          setChatInput={setChatInput}
          handleSendMessage={handleSendMessage}
          isLoading={stream.isLoading}
          cancelRun={cancelRun}
          errorState={errorState}
        />
        
        {/* Right Side - Thread Info & Debug */}
        <ChatSidebarPanel
          threadData={dummyThread}
          displayThread={displayThread}
          isLoading={false}
        />
      </div>
    </div>
  );
}
