"use client";

import { useRouter } from "next/navigation";
import { useState, useMemo } from "react";
import { DEFAULT_AGENT } from "@/common/constants";
import { useThreadsSWR } from "@/hooks/useThreadsSWR";
import { threadsToMetadata } from "@/lib/thread-utils";
import { useFileUpload } from "@/hooks/useFileUpload";
import { QuickActions } from "@/components/v2/quick-actions";
import { DraftsSection } from "@/components/v2/drafts-section";
import {
  HomeHeader,
  AgentSelector,
  ChatInputSection,
  RecentThreads,
} from "@/components/home";



export default function HomePage() {
  const router = useRouter();
  const [selectedAgent, setSelectedAgent] = useState<string>(DEFAULT_AGENT);
  const [quickActionPrompt, setQuickActionPrompt] = useState("");
  const [draftToLoad, setDraftToLoad] = useState("");
  const [autoAccept, setAutoAccept] = useState(false);

  // 文件上传功能
  const {
    contentBlocks,
    setContentBlocks,
    dropRef,
    removeBlock,
    dragOver,
    handlePaste,
  } = useFileUpload();

  // 获取线程列表 - 参考open-swe的useThreadsSWR
  const { threads, isLoading: threadsLoading } = useThreadsSWR({
    assistantId: selectedAgent,
    disableUserFiltering: true,
  });

  // 转换线程数据为显示格式
  const threadsMetadata = useMemo(() => threadsToMetadata(threads), [threads]);
  const displayThreads = threadsMetadata.slice(0, 4);

  const handleThreadClick = (threadId: string) => {
    router.push(`/chat/${threadId}?agent=${selectedAgent}`);
  };

  const handleThreads = () => {
    router.push(`/threads`);
  };

  const handleLoadDraft = (content: string) => {
    setDraftToLoad(content);
  };

  return (
    <div className="flex flex-1 flex-col min-h-screen">
      {/* Header */}
      <HomeHeader />

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto">
        <div className="mx-auto max-w-4xl space-y-6 p-4">

          {/* Agent选择区域 */}
          <AgentSelector
            selectedAgent={selectedAgent}
            onAgentChange={setSelectedAgent}
          />

          {/* 聊天输入区域 */}
          <ChatInputSection
            selectedAgent={selectedAgent}
            contentBlocks={contentBlocks}
            setContentBlocks={setContentBlocks}
            dropRef={dropRef}
            dragOver={dragOver}
            handlePaste={handlePaste}
            removeBlock={removeBlock}
            quickActionPrompt={quickActionPrompt}
            setQuickActionPrompt={setQuickActionPrompt}
            draftToLoad={draftToLoad}
            autoAccept={autoAccept}
            setAutoAccept={setAutoAccept}
          />

          {/* Recent & Running Threads */}
          <RecentThreads
            threads={displayThreads}
            threadsLoading={threadsLoading}
            onThreadClick={handleThreadClick}
            onViewAllThreads={handleThreads}
          />

          {/* Quick Actions */}
          <QuickActions setQuickActionPrompt={setQuickActionPrompt} />

          {/* Drafts Section */}
          <DraftsSection onLoadDraft={handleLoadDraft} />
        </div>
      </div>
    </div>
  );
}
